#!/usr/bin/env python3
"""
专门调试HTML表格解析问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
from app.services.data_details_service import DataDetailsService

def debug_html_parsing():
    """调试HTML表格解析"""

    file_path = 'data/feishu_app_4/account_7/wechat_data_account_7_user_channel_2025-06-20_to_2025-07-20.xlsx'

    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return

    # 读取HTML内容
    with open(file_path, 'r', encoding='utf-8') as f:
        html_content = f.read()

    print("=== HTML内容分析 ===")
    print(f"文件大小: {len(html_content)} 字符")

    # 测试HTML表格解析
    print("\n=== 测试HTML表格解析 ===")
    df = DataDetailsService._parse_html_table(html_content)

    print(f"解析结果: {len(df)} 行, {len(df.columns)} 列")
    if not df.empty:
        print(f"列名: {df.columns.tolist()}")
        print("所有数据:")
        print(df)

        # 测试导入功能
        print("\n=== 测试导入功能 ===")
        from app.database import SessionLocal

        db = SessionLocal()
        try:
            with open(file_path, 'rb') as f:
                content = f.read()

            result = DataDetailsService.import_excel_data(
                db=db,
                account_id=7,
                data_type='user_channel',
                excel_content=content
            )
            print(f"导入结果: {result}")

        except Exception as e:
            print(f"导入测试失败: {e}")
            import traceback
            traceback.print_exc()
        finally:
            db.close()
    else:
        print("解析结果为空")

        # 测试直接使用pandas读取HTML
        print("\n=== 直接使用pandas读取HTML ===")
        try:
            from io import StringIO
            tables = pd.read_html(StringIO(html_content))
            print(f"找到 {len(tables)} 个表格")

            for i, table in enumerate(tables):
                print(f"\n表格 {i+1}: {len(table)} 行, {len(table.columns)} 列")
                print(f"列名: {table.columns.tolist()}")
                print("前5行:")
                print(table.head())

        except Exception as e:
            print(f"直接读取失败: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    debug_html_parsing()
