#!/usr/bin/env python3
"""
测试HTML表格数据转换
"""

import pandas as pd
from io import StringIO

def test_html_conversion():
    """测试HTML表格数据转换"""
    
    file_path = 'data/feishu_app_4/account_7/wechat_data_account_7_user_channel_2025-06-20_to_2025-07-20.xlsx'
    
    # 读取HTML内容
    with open(file_path, 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    # 使用pandas读取HTML表格，不指定header让它自动检测
    tables = pd.read_html(StringIO(html_content), encoding='utf-8')
    df = tables[0]

    print("原始DataFrame:")
    print(f"形状: {df.shape}")
    print(f"列名: {df.columns.tolist()}")
    print("前5行:")
    print(df.head())

    # 如果DataFrame为空但有列名，说明数据在列名中
    if df.empty and len(df.columns) > 0:
        print("\n数据在列名中，尝试转置...")

        # 尝试不同的header设置
        tables_multi = pd.read_html(StringIO(html_content), header=[0, 1, 2], encoding='utf-8')
        df_multi = tables_multi[0]

        print(f"\n多级表头DataFrame:")
        print(f"形状: {df_multi.shape}")
        print(f"列数: {len(df_multi.columns)}")

        # 检查列结构
        columns = df_multi.columns
        print(f"\n列结构分析:")
        for i, col in enumerate(columns[:5]):  # 只显示前5列
            print(f"列 {i}: {col} (长度: {len(col)})")
            if len(col) > 3:
                print(f"  额外数据: {col[3:8]}...")  # 显示第4-8个元素

        # 从第一列提取日期
        if len(columns) > 0:
            first_col = columns[0]
            print(f"\n第一列完整数据: {first_col}")
            dates = []
            if len(first_col) > 3:
                for i in range(3, len(first_col)):
                    date_str = str(first_col[i])
                    if len(date_str) == 10 and date_str.count('-') == 2:
                        dates.append(date_str)
            print(f"提取的日期: {dates}")

        return df_multi

    return df
    
    # 提取指标数据
    metrics = {}
    metric_names = ['新关注人数', '取消关注人数', '净增关注人数', '累积关注人数']
    
    for i, metric_name in enumerate(metric_names):
        if i + 1 < len(columns):
            metric_col = columns[i + 1]
            print(f"\n{metric_name}列: {metric_col}")
            
            # 提取数值（从第4个元素开始，对应日期）
            values = []
            for j in range(3, len(metric_col)):
                if j - 3 < len(dates):  # 确保索引对应
                    try:
                        value = int(metric_col[j])
                        values.append(value)
                    except (ValueError, TypeError):
                        values.append(0)
            
            print(f"{metric_name}数据: {values[:5]}...")  # 只显示前5个
            metrics[metric_name] = values
    
    # 创建最终的DataFrame
    if metrics and len(dates) > 0:
        data = {'时间': dates}
        data.update(metrics)
        final_df = pd.DataFrame(data)
        
        print(f"\n最终DataFrame:")
        print(f"形状: {final_df.shape}")
        print(f"列名: {final_df.columns.tolist()}")
        print("前5行:")
        print(final_df.head())
        
        return final_df
    
    return pd.DataFrame()

if __name__ == "__main__":
    test_html_conversion()
