#!/usr/bin/env python3
"""
调试导入失败的问题
"""

import pandas as pd
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_file_format(file_path):
    """检查文件格式"""
    print(f"\n检查文件: {file_path}")
    
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return
    
    # 检查文件大小
    file_size = os.path.getsize(file_path)
    print(f"文件大小: {file_size} bytes")
    
    # 检查文件头部内容
    with open(file_path, 'rb') as f:
        header = f.read(100)
        print(f"文件头部 (前100字节): {header}")
    
    # 尝试用不同的方式读取文件
    try:
        # 尝试作为Excel文件读取
        df = pd.read_excel(file_path)
        print(f"Excel读取成功: {len(df)} 行, {len(df.columns)} 列")
        print(f"列名: {df.columns.tolist()}")
        if len(df) > 0:
            print("前3行数据:")
            print(df.head(3))
    except Exception as e:
        print(f"Excel读取失败: {e}")
        
        # 尝试作为CSV读取
        try:
            df = pd.read_csv(file_path, encoding='utf-8')
            print(f"CSV读取成功: {len(df)} 行, {len(df.columns)} 列")
            print(f"列名: {df.columns.tolist()}")
        except Exception as e2:
            print(f"CSV读取也失败: {e2}")
            
            # 尝试作为文本文件读取
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read(500)
                    print(f"文本内容 (前500字符): {content}")
            except Exception as e3:
                print(f"文本读取失败: {e3}")

def debug_content_trend_parsing():
    """调试内容数据趋势明细表的解析问题"""
    print("\n=== 调试内容数据趋势明细表 ===")
    
    file_path = 'data/feishu_app_4/account_7/wechat_data_account_7_content_trend_2025-06-20_to_2025-07-20.xlsx'
    
    try:
        df = pd.read_excel(file_path)
        print(f"成功读取Excel: {len(df)} 行, {len(df.columns)} 列")
        print(f"列名: {df.columns.tolist()}")
        
        # 检查日期列
        if '日期' in df.columns:
            print("\n日期列分析:")
            date_col = df['日期']
            print(f"数据类型: {date_col.dtype}")
            print(f"非空值数量: {date_col.notna().sum()}")
            print(f"空值数量: {date_col.isna().sum()}")
            
            print("\n前5个日期值:")
            for i, val in enumerate(date_col.head()):
                print(f"  {i+1}: {repr(val)} (type: {type(val).__name__})")
                
            # 测试解析
            from app.services.data_details_service import DataDetailsService
            print("\n日期解析测试:")
            for i, val in enumerate(date_col.head()):
                try:
                    parsed = DataDetailsService._parse_date(val)
                    print(f"  {i+1}: {repr(val)} -> {parsed}")
                except Exception as e:
                    print(f"  {i+1}: {repr(val)} -> 解析失败: {e}")
        else:
            print("未找到'日期'列")
            
    except Exception as e:
        print(f"读取Excel失败: {e}")

def test_import_fixes():
    """测试修复后的导入功能"""
    print("\n=== 测试修复后的导入功能 ===")

    from app.database import SessionLocal
    from app.services.data_details_service import DataDetailsService

    db = SessionLocal()
    try:
        # 测试用户增长表（HTML格式）
        user_channel_file = 'data/feishu_app_4/account_7/wechat_data_account_7_user_channel_2025-06-20_to_2025-07-20.xlsx'
        if os.path.exists(user_channel_file):
            print(f"\n测试用户增长表导入: {user_channel_file}")
            with open(user_channel_file, 'rb') as f:
                content = f.read()

            result = DataDetailsService.import_excel_data(
                db=db,
                account_id=7,
                data_type='user_channel',
                excel_content=content
            )
            print(f"用户增长表导入结果: {result}")

        # 测试内容数据趋势明细表
        content_trend_file = 'data/feishu_app_4/account_7/wechat_data_account_7_content_trend_2025-06-20_to_2025-07-20.xlsx'
        if os.path.exists(content_trend_file):
            print(f"\n测试内容数据趋势明细表导入: {content_trend_file}")
            with open(content_trend_file, 'rb') as f:
                content = f.read()

            result = DataDetailsService.import_excel_data(
                db=db,
                account_id=7,
                data_type='content_trend',
                excel_content=content
            )
            print(f"内容数据趋势明细表导入结果: {result}")

    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

def main():
    """主函数"""
    print("=== 调试导入失败问题 ===")

    # 检查用户增长表文件
    user_channel_file = 'data/feishu_app_4/account_7/wechat_data_account_7_user_channel_2025-06-20_to_2025-07-20.xlsx'
    check_file_format(user_channel_file)

    # 检查内容数据趋势明细表文件
    content_trend_file = 'data/feishu_app_4/account_7/wechat_data_account_7_content_trend_2025-06-20_to_2025-07-20.xlsx'
    check_file_format(content_trend_file)

    # 专门调试内容数据趋势明细表的解析问题
    debug_content_trend_parsing()

    # 测试修复后的导入功能
    test_import_fixes()

if __name__ == "__main__":
    main()
