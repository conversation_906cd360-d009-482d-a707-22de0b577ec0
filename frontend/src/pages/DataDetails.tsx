import React, { useState, useEffect } from 'react';
import { Layout, Menu, Table, Card, Select, Input, Button, message, Spin, Tag, Space } from 'antd';
import { SearchOutlined, ReloadOutlined, DownloadOutlined } from '@ant-design/icons';
import api from '../services/api';

const { Sider, Content } = Layout;
const { Search } = Input;
const { Option } = Select;

interface Account {
  id: number;
  name: string;
  login_status: boolean;
  last_login_time: string | null;
  created_at: string;
}

interface DataItem {
  id: number;
  account_id: number;
  [key: string]: any;
}

interface DataConfig {
  name: string;
  description: string;
  columns: Array<{
    key: string;
    title: string;
    type: string;
  }>;
}

interface DataTypeConfig {
  [key: string]: DataConfig;
}

const DataDetails: React.FC = () => {
  const [selectedPlatform, setSelectedPlatform] = useState<string>('wechat_mp');
  const [selectedDataType, setSelectedDataType] = useState<string>('content_trend');
  const [selectedAccount, setSelectedAccount] = useState<number | null>(null);
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [dataList, setDataList] = useState<DataItem[]>([]);
  const [dataConfig, setDataConfig] = useState<DataTypeConfig>({});
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });
  const [searchText, setSearchText] = useState<string>('');

  // 平台配置
  const platformConfig = {
    wechat_mp: {
      name: '微信公众号',
      dataTypes: {
        content_trend: '内容数据趋势明细',
        content_source: '内容流量来源明细',
        content_detail: '内容已通知内容明细',
        user_channel: '用户增长明细'
      }
    },
    wechat_channels: {
      name: '视频号',
      dataTypes: {}
    },
    xiaohongshu: {
      name: '小红书',
      dataTypes: {}
    }
  };

  // 菜单项
  const menuItems = [
    {
      key: 'wechat_mp',
      label: '微信公众号',
      children: [
        { key: 'wechat_mp_content_trend', label: '内容数据趋势明细' },
        { key: 'wechat_mp_content_source', label: '内容流量来源明细' },
        { key: 'wechat_mp_content_detail', label: '内容已通知内容明细' },
        { key: 'wechat_mp_user_channel', label: '用户增长明细' }
      ]
    },
    {
      key: 'wechat_channels',
      label: '视频号',
      disabled: true,
      children: [
        { key: 'wechat_channels_placeholder', label: '敬请期待', disabled: true }
      ]
    },
    {
      key: 'xiaohongshu',
      label: '小红书',
      disabled: true,
      children: [
        { key: 'xiaohongshu_placeholder', label: '敬请期待', disabled: true }
      ]
    }
  ];

  useEffect(() => {
    fetchDataConfig();
    fetchAccounts();
  }, []);

  useEffect(() => {
    if (selectedAccount && selectedDataType) {
      fetchDataList();
    }
  }, [selectedAccount, selectedDataType, pagination.current, pagination.pageSize, searchText]);

  const fetchDataConfig = async () => {
    try {
      const response = await api.get('/data-details/wechat-mp/config');
      if (response.data.success) {
        setDataConfig(response.data.data_types);
      }
    } catch (error: any) {
      console.error('获取数据配置失败:', error);
      if (error.response) {
        console.error('错误响应:', error.response.data);
        message.error(`获取数据配置失败: ${error.response.data.detail || error.message}`);
      } else {
        message.error('获取数据配置失败');
      }
    }
  };

  const fetchAccounts = async () => {
    try {
      const response = await api.get('/data-details/wechat-mp/accounts');
      if (response.data.success) {
        setAccounts(response.data.accounts);
        if (response.data.accounts.length > 0) {
          setSelectedAccount(response.data.accounts[0].id);
        }
      }
    } catch (error: any) {
      console.error('获取账号列表失败:', error);
      if (error.response) {
        console.error('错误响应:', error.response.data);
        message.error(`获取账号列表失败: ${error.response.data.detail || error.message}`);
      } else {
        message.error('获取账号列表失败');
      }
    }
  };

  const fetchDataList = async () => {
    if (!selectedAccount || !selectedDataType) return;

    setLoading(true);
    try {
      const params = {
        account_id: selectedAccount,
        page: pagination.current,
        page_size: pagination.pageSize,
        search: searchText || undefined,
        sort_field: 'created_at',
        sort_order: 'desc'
      };

      const response = await api.get(`/data-details/wechat-mp/${selectedDataType}`, { params });
      
      if (response.data.success) {
        setDataList(response.data.data);
        setPagination(prev => ({
          ...prev,
          total: response.data.total,
          current: response.data.page
        }));
      } else {
        message.error(response.data.error || '获取数据失败');
      }
    } catch (error) {
      message.error('获取数据列表失败');
    } finally {
      setLoading(false);
    }
  };

  const handleMenuClick = ({ key }: { key: string }) => {
    const parts = key.split('_');
    if (parts.length >= 3) {
      const platform = parts.slice(0, 2).join('_'); // wechat_mp
      const dataType = parts.slice(2).join('_'); // content_trend
      
      setSelectedPlatform(platform);
      setSelectedDataType(dataType);
      setPagination(prev => ({ ...prev, current: 1 }));
    }
  };

  const handleTableChange = (paginationInfo: any) => {
    setPagination(prev => ({
      ...prev,
      current: paginationInfo.current,
      pageSize: paginationInfo.pageSize
    }));
  };

  const handleSearch = (value: string) => {
    setSearchText(value);
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  const handleRefresh = () => {
    fetchDataList();
  };

  // 生成表格列配置
  const generateColumns = () => {
    const config = dataConfig[selectedDataType];
    if (!config) return [];

    return config.columns.map(col => ({
      title: col.title,
      dataIndex: col.key,
      key: col.key,
      render: (value: any) => {
        if (col.type === 'date' || col.type === 'datetime') {
          return value ? new Date(value).toLocaleString() : '-';
        }
        if (col.type === 'number') {
          return typeof value === 'number' ? value.toLocaleString() : value || 0;
        }
        if (col.type === 'url') {
          return value ? (
            <a href={value} target="_blank" rel="noopener noreferrer">
              查看链接
            </a>
          ) : '-';
        }
        return value || '-';
      },
      sorter: col.type === 'number' || col.type === 'date' || col.type === 'datetime',
      width: col.type === 'text' ? 200 : 120
    }));
  };

  const currentConfig = dataConfig[selectedDataType];

  return (
    <div style={{ height: '100vh' }}>
      <Layout style={{ height: '100%' }}>
        <Sider width={250} theme="light" style={{ borderRight: '1px solid #f0f0f0' }}>
          <div style={{ padding: '16px', borderBottom: '1px solid #f0f0f0' }}>
            <h3>数据明细</h3>
          </div>
          <Menu
            mode="inline"
            selectedKeys={[`${selectedPlatform}_${selectedDataType}`]}
            items={menuItems}
            onClick={handleMenuClick}
            style={{ height: 'calc(100% - 64px)', borderRight: 0 }}
          />
        </Sider>
        
        <Layout>
          <Content style={{ padding: '24px', background: '#fff' }}>
            {currentConfig && (
              <>
                <Card 
                  title={currentConfig.name}
                  extra={
                    <Space>
                      <Select
                        value={selectedAccount}
                        onChange={setSelectedAccount}
                        style={{ width: 200 }}
                        placeholder="选择账号"
                      >
                        {accounts.map(account => (
                          <Option key={account.id} value={account.id}>
                            <Space>
                              {account.name}
                              <Tag color={account.login_status ? 'green' : 'red'}>
                                {account.login_status ? '已登录' : '未登录'}
                              </Tag>
                            </Space>
                          </Option>
                        ))}
                      </Select>
                      <Search
                        placeholder="搜索..."
                        allowClear
                        onSearch={handleSearch}
                        style={{ width: 200 }}
                      />
                      <Button icon={<ReloadOutlined />} onClick={handleRefresh}>
                        刷新
                      </Button>
                    </Space>
                  }
                  style={{ marginBottom: 16 }}
                >
                  <p style={{ margin: 0, color: '#666' }}>{currentConfig.description}</p>
                </Card>

                <Table
                  columns={generateColumns()}
                  dataSource={dataList}
                  rowKey="id"
                  loading={loading}
                  pagination={{
                    ...pagination,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total, range) => 
                      `第 ${range[0]}-${range[1]} 条，共 ${total} 条数据`
                  }}
                  onChange={handleTableChange}
                  scroll={{ x: 'max-content' }}
                  size="small"
                />
              </>
            )}
            
            {!currentConfig && (
              <div style={{ textAlign: 'center', padding: '50px' }}>
                <Spin size="large" />
                <p style={{ marginTop: 16 }}>加载中...</p>
              </div>
            )}
          </Content>
        </Layout>
      </Layout>
    </div>
  );
};

export default DataDetails;
