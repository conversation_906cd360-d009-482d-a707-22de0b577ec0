[{"/Users/<USER>/Codes/py/social-media-manager/frontend/src/index.tsx": "1", "/Users/<USER>/Codes/py/social-media-manager/frontend/src/App.tsx": "2", "/Users/<USER>/Codes/py/social-media-manager/frontend/src/components/Layout.tsx": "3", "/Users/<USER>/Codes/py/social-media-manager/frontend/src/components/ProtectedRoute.tsx": "4", "/Users/<USER>/Codes/py/social-media-manager/frontend/src/contexts/AuthContext.tsx": "5", "/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/Dashboard.tsx": "6", "/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/AccountManage.tsx": "7", "/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/Login.tsx": "8", "/Users/<USER>/Codes/py/social-media-manager/frontend/src/services/api.ts": "9", "/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/FeishuAppManage.tsx": "10", "/Users/<USER>/Codes/py/social-media-manager/frontend/src/services/feishuAppService.ts": "11", "/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/DataDetails.tsx": "12"}, {"size": 272, "mtime": *************, "results": "13", "hashOfConfig": "14"}, {"size": 1280, "mtime": *************, "results": "15", "hashOfConfig": "16"}, {"size": 2300, "mtime": *************, "results": "17", "hashOfConfig": "16"}, {"size": 646, "mtime": *************, "results": "18", "hashOfConfig": "14"}, {"size": 1976, "mtime": *************, "results": "19", "hashOfConfig": "14"}, {"size": 5101, "mtime": *************, "results": "20", "hashOfConfig": "16"}, {"size": 29678, "mtime": *************, "results": "21", "hashOfConfig": "16"}, {"size": 2050, "mtime": 1752747944753, "results": "22", "hashOfConfig": "14"}, {"size": 763, "mtime": 1753039109233, "results": "23", "hashOfConfig": "16"}, {"size": 10389, "mtime": 1753033841869, "results": "24", "hashOfConfig": "16"}, {"size": 2912, "mtime": 1753033799016, "results": "25", "hashOfConfig": "16"}, {"size": 10490, "mtime": 1753073751992, "results": "26", "hashOfConfig": "16"}, {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1sxxfvo", {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ix7n9v", {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/Codes/py/social-media-manager/frontend/src/index.tsx", [], [], "/Users/<USER>/Codes/py/social-media-manager/frontend/src/App.tsx", [], [], "/Users/<USER>/Codes/py/social-media-manager/frontend/src/components/Layout.tsx", [], [], "/Users/<USER>/Codes/py/social-media-manager/frontend/src/components/ProtectedRoute.tsx", [], [], "/Users/<USER>/Codes/py/social-media-manager/frontend/src/contexts/AuthContext.tsx", [], [], "/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/Dashboard.tsx", [], [], "/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/AccountManage.tsx", ["63"], [], "/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/Login.tsx", [], [], "/Users/<USER>/Codes/py/social-media-manager/frontend/src/services/api.ts", ["64"], [], "/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/FeishuAppManage.tsx", [], [], "/Users/<USER>/Codes/py/social-media-manager/frontend/src/services/feishuAppService.ts", [], [], "/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/DataDetails.tsx", ["65", "66", "67", "68"], [], {"ruleId": "69", "severity": 1, "message": "70", "line": 337, "column": 9, "nodeType": "71", "messageId": "72", "endLine": 337, "endColumn": 26}, {"ruleId": "69", "severity": 1, "message": "73", "line": 2, "column": 10, "nodeType": "71", "messageId": "72", "endLine": 2, "endColumn": 17}, {"ruleId": "69", "severity": 1, "message": "74", "line": 3, "column": 10, "nodeType": "71", "messageId": "72", "endLine": 3, "endColumn": 24}, {"ruleId": "69", "severity": 1, "message": "75", "line": 3, "column": 42, "nodeType": "71", "messageId": "72", "endLine": 3, "endColumn": 58}, {"ruleId": "69", "severity": 1, "message": "76", "line": 54, "column": 9, "nodeType": "71", "messageId": "72", "endLine": 54, "endColumn": 23}, {"ruleId": "77", "severity": 1, "message": "78", "line": 113, "column": 6, "nodeType": "79", "endLine": 113, "endColumn": 94, "suggestions": "80"}, "@typescript-eslint/no-unused-vars", "'handleForceLogout' is assigned a value but never used.", "Identifier", "unusedVar", "'message' is defined but never used.", "'SearchOutlined' is defined but never used.", "'DownloadOutlined' is defined but never used.", "'platformConfig' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchDataList'. Either include it or remove the dependency array. Mutable values like 'pagination.current' aren't valid dependencies because mutating them doesn't re-render the component.", "ArrayExpression", ["81"], {"desc": "82", "fix": "83"}, "Update the dependencies array to be: [selectedAccount, selectedDataType, pagination.pageSize, searchText, fetchDataList]", {"range": "84", "text": "85"}, [2780, 2868], "[selectedAccount, selectedDataType, pagination.pageSize, searchText, fetchDataList]"]